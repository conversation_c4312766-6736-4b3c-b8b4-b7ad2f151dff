// AdminRechargeDashboard.jsx

import React, { useState, useEffect } from 'react';
import getServerBaseUrl from '@/envConfig';
import toast from 'react-hot-toast';

interface User {
  _id: string;
  fullName: string;
  phoneNumber: string;
}

interface Recharge {
  _id: string;
  userId: User;
  mobileNumber: string;
  operator: string;
  rechargeAmount: number;
  pointsDeducted: number;
  status: 'pending' | 'completed' | 'denied';
  rechargeDate: string;
  adminNotes?: string;
}

const AdminRechargeDashboard: React.FC = () => {
  const [recharges, setRecharges] = useState<Recharge[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRecharge, setSelectedRecharge] = useState<Recharge | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);

  const backendUrl = getServerBaseUrl();

  // Fetch recharge requests from backend
  const fetchRecharges = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('admin_token'); // Use consistent token name
      const url = `${backendUrl}${backendUrl.endsWith('/') ? '' : '/'}api/users/recharge`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        setRecharges(data.data.recharges);
      } else {
        toast.error(data.message || 'Failed to fetch recharges');
      }
    } catch (error) {
      console.error('Error fetching recharges:', error);
      toast.error('Failed to fetch recharges');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecharges();
  }, []);

  // Update recharge status
  const handleStatusUpdate = async (id: string, status: 'completed' | 'denied', notes: string) => {
    if (processing) return;
    
    setProcessing(id);
    try {
      const token = localStorage.getItem('admin_token');
      const url = `${backendUrl}${backendUrl.endsWith('/') ? '' : '/'}api/users/recharge/${id}`;
      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          status,
          adminNotes: notes,
          processedBy: 'admin_user_id', // TODO: Get from auth context
        }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success(`Recharge ${status === 'completed' ? 'approved' : 'denied'} successfully`);
        fetchRecharges();
        setShowModal(false);
        setAdminNotes('');
        setSelectedRecharge(null);
      } else {
        toast.error(result.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating recharge:', error);
      toast.error('Failed to update recharge status');
    } finally {
      setProcessing(null);
    }
  };

  return (
    <div className="dashboard-container">
      <header className="header-gradient">
        <h2>Recharge Management</h2>
      </header>

      {loading ? (
        <div className="loader">Loading...</div>
      ) : recharges.length === 0 ? (
        <div className="empty-text">No recharge requests found</div>
      ) : (
        <div className="recharge-list">
          {recharges.map((item) => (
            <div
              key={item._id}
              className="recharge-item"
              onClick={() => {
                setSelectedRecharge(item);
                setAdminNotes(item.adminNotes || '');
                setShowModal(true);
              }}
            >
              <div className="item-left">
                <div className={`status-dot ${item.status}`}></div>
                <div>
                  <div className="user-name">
                    {item.userId?.fullName || 'Unknown User'}
                  </div>
                  <div className="details">
                    {item.mobileNumber} • {item.operator}
                  </div>
                  <div className="amount">
                    ₹{item.rechargeAmount} ({item.pointsDeducted} pts)
                  </div>
                  <div className="date">
                    {new Date(item.rechargeDate).toLocaleString('en-IN')}
                  </div>
                </div>
              </div>
              <div className={`status-label ${item.status}`}>
                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      {showModal && selectedRecharge && (
        <div className="modal-overlay" onClick={() => setShowModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Recharge Details</h3>
              <button onClick={() => setShowModal(false)}>X</button>
            </div>

            <div className="modal-body">
              <p><strong>User:</strong> {selectedRecharge.userId?.fullName}</p>
              <p><strong>Phone:</strong> {selectedRecharge.userId?.phoneNumber}</p>
              <p><strong>Mobile:</strong> {selectedRecharge.mobileNumber}</p>
              <p><strong>Operator:</strong> {selectedRecharge.operator}</p>
              <p><strong>Amount:</strong> ₹{selectedRecharge.rechargeAmount} ({selectedRecharge.pointsDeducted} pts)</p>
              <p><strong>Status:</strong> {selectedRecharge.status}</p>
              <p><strong>Date:</strong> {new Date(selectedRecharge.rechargeDate).toLocaleString('en-IN')}</p>

              <textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder="Add admin notes..."
              />

              <div className="action-buttons">
                <button
                  className="btn success"
                  disabled={selectedRecharge.status !== 'pending'}
                  onClick={() =>
                    handleStatusUpdate(selectedRecharge._id, 'completed', adminNotes)
                  }
                >
                  Mark as Done
                </button>

                <button
                  className="btn danger"
                  disabled={selectedRecharge.status !== 'pending'}
                  onClick={() =>
                    handleStatusUpdate(selectedRecharge._id, 'denied', adminNotes)
                  }
                >
                  Deny
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminRechargeDashboard;
