/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Accessible color palette based on COLOR_ACCESSIBILITY_GUIDE.md
        primary: {
          50: '#e8f5ea',
          100: '#c3e6c8',
          200: '#9dd6a3',
          300: '#76c67e',
          400: '#58ba62',
          500: '#1ca63a', // Primary Green
          600: '#189533',
          700: '#14822c',
          800: '#106f25',
          900: '#0a5018',
        },
        secondary: {
          50: '#fdf2ed',
          100: '#f9ddd0',
          200: '#f5c7b0',
          300: '#f1b090',
          400: '#ed9a78',
          500: '#df5921', // Secondary Orange
          600: '#c8501e',
          700: '#b1471a',
          800: '#9a3e17',
          900: '#7a3012',
        },
        neutral: {
          50: '#f8f9f9',
          100: '#f1f2f3',
          200: '#e4e6e7',
          300: '#d7dadb',
          400: '#cacecf',
          500: '#7e8689', // Grey
          600: '#6f7578',
          700: '#606467',
          800: '#515356',
          900: '#424245',
        },
        accent: {
          50: '#fefbf0',
          100: '#fdf4d9',
          200: '#fbedb3',
          300: '#f9e68c',
          400: '#f7df66',
          500: '#d5a81a', // Yellow
          600: '#c09717',
          700: '#ab8614',
          800: '#967511',
          900: '#6d540c',
        },
        // Semantic colors using the accessible palette
        success: '#1ca63a',
        warning: '#d5a81a',
        error: '#df5921',
        info: '#7e8689',
      },
    },
  },
  plugins: [],
}
